import { ChangeDetectorRef, Component } from "@angular/core";
import { CommonModule } from "@angular/common";
import { IonContent } from "@ionic/angular/standalone";
import { HeaderComponent } from "../../components/header/header";
import { TableComponent } from "../../components/table/table";
import { BillingComponent } from "../../components/billing/billing";
import { ButtonModule } from 'primeng/button';
import { OrderService } from "src/app/services/order.service";
import { CommonService } from "src/app/services/common";
import { lastValueFrom } from "rxjs";
@Component({
    selector: 'app-invoices',
    standalone: true,
    templateUrl: './invoices.html',
    imports: [CommonModule, IonContent, HeaderComponent, TableComponent, BillingComponent, ButtonModule]
})
export class InvoicesComponent {
    invoices: any[] = [];
    invoicesColumns: any[] = [];
    cartItems: any[] = [];
    isLoading = false;
    ordersData: any = null;

    constructor(
        private orderService: OrderService,
        private commonService: CommonService,
        private cdr: ChangeDetectorRef
    ) {
       
    }
    ionViewDidEnter(){
        this.invoicesColumns = [
            { field: 'order_id', header: 'Order ID' },
            { field: 'customer_name', header: 'Customer' },
            { field: 'created_at', header: 'Date' },
            { field: 'status', header: 'Status' },
            { field: 'total_amount', header: 'Total' },
        ];
        this.cartItems = []
        this.loadOrders();
    }
    loadOrders() {
        this.orderService.getOrders().then((orders: any) => {
            this.invoices = orders?.data;
        }).catch((error: any) => {
            console.error('Error fetching orders:', error);
            this.commonService.toast({ severity: 'error', summary: 'Error', detail: 'Failed to load orders. Please try again.' });
        });
    }
    onRowClick(ev: any) {
        this.getRowData(ev.rowData?.order_id)
    }
    async getRowData(id: string){
        const data: any = await this.orderService.getOrderDetails(id)
        this.cartItems = data?.data?.items || [];
        this.cdr.detectChanges()
    }
}